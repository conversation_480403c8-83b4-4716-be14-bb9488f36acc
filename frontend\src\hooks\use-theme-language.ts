/**
 * Hook for components to easily respond to theme and language changes
 */

import { useEffect, useState } from 'react'
import { useTheme } from '@/contexts/theme-context'
import { getThemeLanguageClasses, getCurrentThemeFromDOM, getCurrentLanguageFromDOM } from '@/lib/theme-utils'

export function useThemeLanguage() {
  const { theme, language, actualTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const [forceUpdate, setForceUpdate] = useState(0)

  // Set mounted after hydration
  useEffect(() => {
    setMounted(true)
    console.log('🔧 useThemeLanguage mounted with:', { theme, language, actualTheme })
  }, [])

  // Listen for theme and language changes
  useEffect(() => {
    if (!mounted) return

    const handleThemeChange = (event: CustomEvent) => {
      console.log('🔧 useThemeLanguage received theme change:', event.detail)
      setForceUpdate(prev => prev + 1)
    }

    const handleLanguageChange = (event: CustomEvent) => {
      console.log('🔧 useThemeLanguage received language change:', event.detail)
      setForceUpdate(prev => prev + 1)
    }

    window.addEventListener('theme-changed', handleThemeChange as EventListener)
    window.addEventListener('language-changed', handleLanguageChange as EventListener)

    return () => {
      window.removeEventListener('theme-changed', handleThemeChange as EventListener)
      window.removeEventListener('language-changed', handleLanguageChange as EventListener)
    }
  }, [mounted])

  // Update when theme or language changes from context
  useEffect(() => {
    if (mounted) {
      console.log('🔧 useThemeLanguage context update:', { theme, language, actualTheme })
      setForceUpdate(prev => prev + 1)
    }
  }, [theme, language, actualTheme, mounted])

  // Always return server-safe defaults until mounted to prevent hydration mismatch
  const currentTheme = mounted ? (actualTheme || 'light') : 'light'
  const currentLanguage = mounted ? (language || 'mm') : 'mm'

  // Helper function to get classes with theme and language support
  const getClasses = (baseClasses: string) => {
    if (!mounted) {
      // Return server-safe classes that match the default state
      return `${baseClasses} transition-colors duration-300 font-myanmar`
    }
    return getThemeLanguageClasses(baseClasses, currentTheme, currentLanguage)
  }

  // Helper function to check if Myanmar language is active
  const isMyanmarLanguage = mounted ? currentLanguage === 'mm' : true

  // Helper function to check if dark theme is active
  const isDarkTheme = mounted ? currentTheme === 'dark' : false

  return {
    theme: currentTheme,
    language: currentLanguage,
    isMyanmarLanguage,
    isDarkTheme,
    getClasses,
    mounted,
    forceUpdate // Can be used to force re-renders when needed
  }
}

/**
 * Hook specifically for text elements that need language-aware styling
 */
export function useLanguageText() {
  const { language, isMyanmarLanguage, getClasses } = useThemeLanguage()

  const getTextClasses = (baseClasses: string = '') => {
    const languageClasses = isMyanmarLanguage ? 'font-myanmar' : ''
    return getClasses(`${baseClasses} ${languageClasses}`.trim())
  }

  return {
    language,
    isMyanmarLanguage,
    getTextClasses
  }
}

/**
 * Hook for theme-aware styling
 */
export function useThemeStyles() {
  const { theme, isDarkTheme, getClasses } = useThemeLanguage()

  const getThemeClasses = (lightClasses: string, darkClasses: string) => {
    const themeSpecificClasses = isDarkTheme ? darkClasses : lightClasses
    return getClasses(themeSpecificClasses)
  }

  return {
    theme,
    isDarkTheme,
    getThemeClasses,
    getClasses
  }
}
