'use client'

import { useEffect } from 'react'
import { useTheme } from '@/contexts/theme-context'
import { refreshThemeAndLanguage, validateThemeAndLanguage } from '@/lib/theme-init'

/**
 * Component that ensures theme and language are properly applied
 * Should be included in the main layout to enforce consistency
 */
export function ThemeEnforcer() {
  const { theme, language, actualTheme } = useTheme()

  useEffect(() => {
    // Only validate after a short delay to let everything initialize
    const timer = setTimeout(() => {
      const isValid = validateThemeAndLanguage()
      if (!isValid) {
        console.log('🔧 ThemeEnforcer: Fixing invalid theme/language state')
        refreshThemeAndLanguage()
      }
    }, 500)

    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // Only refresh if we have valid values and they're different from DOM
    if (theme && language) {
      const timer = setTimeout(() => {
        refreshThemeAndLanguage()
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [theme, language, actualTheme])

  // This component doesn't render anything
  return null
}

/**
 * Hook to force theme refresh
 */
export function useThemeRefresh() {
  const { theme, language } = useTheme()

  const forceRefresh = () => {
    refreshThemeAndLanguage()
  }

  useEffect(() => {
    // Auto-refresh when theme or language changes
    forceRefresh()
  }, [theme, language])

  return { forceRefresh }
}
