/**
 * Theme and language initialization utilities
 * Ensures consistent theme and language application across all pages
 */

import { applyThemeToDOM, applyLanguageToDOM, type Theme, type Language } from './theme-utils'

/**
 * Initialize theme and language immediately on page load
 * This prevents flash of unstyled content and ensures consistency
 */
export function initializeThemeAndLanguageImmediate() {
  if (typeof window === 'undefined') return

  try {
    // Get saved preferences
    const savedTheme = localStorage.getItem('bitstech-theme') as Theme || 'light'
    const savedLanguage = localStorage.getItem('bitstech-language') as Language || 'mm'

    // Apply theme immediately
    const actualTheme = applyThemeToDOM(savedTheme)
    
    // Apply language immediately
    applyLanguageToDOM(savedLanguage)

    // Force immediate style application
    document.documentElement.style.colorScheme = actualTheme

    console.log('🎨 Theme and language initialized:', { theme: actualTheme, language: savedLanguage })

    return { theme: actualTheme, language: savedLanguage }
  } catch (error) {
    console.error('Error initializing theme and language:', error)
    
    // Fallback to defaults
    applyThemeToDOM('light')
    applyLanguageToDOM('mm')
    
    return { theme: 'light' as const, language: 'mm' as const }
  }
}

/**
 * Force refresh all theme and language classes
 * Useful when switching between pages or after context updates
 */
export function refreshThemeAndLanguage() {
  if (typeof window === 'undefined') return

  try {
    const savedTheme = localStorage.getItem('bitstech-theme') as Theme || 'light'
    const savedLanguage = localStorage.getItem('bitstech-language') as Language || 'mm'

    // Re-apply everything
    const actualTheme = applyThemeToDOM(savedTheme)
    applyLanguageToDOM(savedLanguage)

    // Dispatch events to force component updates
    window.dispatchEvent(new CustomEvent('theme-changed', {
      detail: { theme: actualTheme }
    }))

    window.dispatchEvent(new CustomEvent('language-changed', {
      detail: { language: savedLanguage }
    }))

    console.log('🔄 Theme and language refreshed:', { theme: actualTheme, language: savedLanguage })
  } catch (error) {
    console.error('Error refreshing theme and language:', error)
  }
}

/**
 * Ensure theme and language are applied to specific elements
 * Useful for dynamically created content
 */
export function applyThemeToElement(element: HTMLElement, theme?: 'light' | 'dark', language?: Language) {
  if (!element) return

  const currentTheme = theme || (document.documentElement.classList.contains('dark') ? 'dark' : 'light')
  const currentLanguage = language || (document.documentElement.classList.contains('lang-mm') ? 'mm' : 'en')

  // Apply theme classes
  element.classList.remove('light', 'dark')
  element.classList.add(currentTheme)
  element.setAttribute('data-theme', currentTheme)

  // Apply language classes
  element.classList.remove('lang-en', 'lang-mm')
  element.classList.add(`lang-${currentLanguage}`)
  element.setAttribute('data-language', currentLanguage)

  // Apply font classes for Myanmar
  if (currentLanguage === 'mm') {
    element.classList.add('font-myanmar')
  } else {
    element.classList.remove('font-myanmar')
  }
}

/**
 * Watch for theme and language changes and apply them immediately
 */
export function setupThemeWatcher() {
  if (typeof window === 'undefined') return

  // Watch for localStorage changes
  const handleStorageChange = (event: StorageEvent) => {
    if (event.key === 'bitstech-theme' || event.key === 'bitstech-language') {
      refreshThemeAndLanguage()
    }
  }

  // Watch for custom events
  const handleThemeChange = () => {
    setTimeout(refreshThemeAndLanguage, 0)
  }

  const handleLanguageChange = () => {
    setTimeout(refreshThemeAndLanguage, 0)
  }

  // Add event listeners
  window.addEventListener('storage', handleStorageChange)
  window.addEventListener('theme-changed', handleThemeChange)
  window.addEventListener('language-changed', handleLanguageChange)

  // Cleanup function
  return () => {
    window.removeEventListener('storage', handleStorageChange)
    window.removeEventListener('theme-changed', handleThemeChange)
    window.removeEventListener('language-changed', handleLanguageChange)
  }
}

// Remove auto-initialization to prevent conflicts with ThemeProvider

/**
 * Utility to check if theme and language are properly applied
 */
export function validateThemeAndLanguage(): boolean {
  if (typeof window === 'undefined') return false

  const root = document.documentElement
  const body = document.body

  // Check if theme classes are applied
  const hasThemeClass = root.classList.contains('light') || root.classList.contains('dark')
  const hasThemeAttribute = root.hasAttribute('data-theme')

  // Check if language classes are applied
  const hasLanguageClass = root.classList.contains('lang-en') || root.classList.contains('lang-mm')
  const hasLanguageAttribute = root.hasAttribute('data-language')

  const isValid = hasThemeClass && hasThemeAttribute && hasLanguageClass && hasLanguageAttribute

  if (!isValid) {
    console.warn('⚠️ Theme or language not properly applied, forcing refresh...')
    refreshThemeAndLanguage()
  }

  return isValid
}

/**
 * Debug function to log current theme and language state
 */
export function debugThemeAndLanguage() {
  if (typeof window === 'undefined') return

  const root = document.documentElement
  const body = document.body

  console.log('🔍 Theme and Language Debug Info:', {
    rootClasses: Array.from(root.classList),
    bodyClasses: Array.from(body.classList),
    rootAttributes: {
      'data-theme': root.getAttribute('data-theme'),
      'data-language': root.getAttribute('data-language'),
      'lang': root.getAttribute('lang')
    },
    localStorage: {
      theme: localStorage.getItem('bitstech-theme'),
      language: localStorage.getItem('bitstech-language')
    },
    colorScheme: root.style.colorScheme
  })
}
