/**
 * Theme utilities for consistent theme and language application
 */

export type Theme = 'light' | 'dark' | 'system'
export type Language = 'en' | 'mm'

/**
 * Apply theme classes to DOM elements
 */
export function applyThemeToDOM(theme: Theme) {
  if (typeof window === 'undefined') return 'light'

  const root = document.documentElement
  const body = document.body

  // Remove all theme classes
  root.classList.remove('light', 'dark')
  body.classList.remove('light', 'dark')

  let actualTheme: 'light' | 'dark'

  if (theme === 'system') {
    actualTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  } else {
    actualTheme = theme
  }

  // Add theme classes immediately
  root.classList.add(actualTheme)
  body.classList.add(actualTheme)

  // Set data attributes for more reliable theme detection
  root.setAttribute('data-theme', actualTheme)
  body.setAttribute('data-theme', actualTheme)

  // Set color scheme for better browser integration
  root.style.colorScheme = actualTheme

  // Force immediate style recalculation
  if (actualTheme === 'dark') {
    root.style.backgroundColor = 'rgb(17 24 39)' // gray-900
    root.style.color = 'rgb(243 244 246)' // gray-100
    body.style.backgroundColor = 'rgb(17 24 39)' // gray-900
    body.style.color = 'rgb(243 244 246)' // gray-100
  } else {
    root.style.backgroundColor = 'rgb(255 255 255)' // white
    root.style.color = 'rgb(17 24 39)' // gray-900
    body.style.backgroundColor = 'rgb(255 255 255)' // white
    body.style.color = 'rgb(17 24 39)' // gray-900
  }

  console.log('🎨 Applied theme to DOM:', actualTheme)
  return actualTheme
}

/**
 * Apply language classes to DOM elements
 */
export function applyLanguageToDOM(language: Language) {
  if (typeof window === 'undefined') return

  const root = document.documentElement
  const body = document.body

  // Remove all language classes including font classes
  root.classList.remove('lang-en', 'lang-mm', 'font-myanmar')
  body.classList.remove('lang-en', 'lang-mm', 'font-myanmar')

  // Add language classes
  root.classList.add(`lang-${language}`)
  body.classList.add(`lang-${language}`)

  // Add Myanmar font if needed
  if (language === 'mm') {
    root.classList.add('font-myanmar')
    body.classList.add('font-myanmar')

    // Force Myanmar font family
    root.style.fontFamily = "'Noto Sans Myanmar', 'Pyidaungsu', 'Myanmar3', sans-serif"
    body.style.fontFamily = "'Noto Sans Myanmar', 'Pyidaungsu', 'Myanmar3', sans-serif"
  } else {
    // Reset to default font for English
    root.style.fontFamily = ''
    body.style.fontFamily = ''
  }

  // Set data attributes
  root.setAttribute('data-language', language)
  body.setAttribute('data-language', language)

  // Update HTML lang attribute
  root.setAttribute('lang', language === 'mm' ? 'my' : 'en')

  console.log('🌐 Applied language to DOM:', language)
}

/**
 * Get theme-aware CSS classes
 */
export function getThemeClasses(baseClasses: string, theme?: 'light' | 'dark') {
  return `${baseClasses} transition-colors duration-300`
}

/**
 * Get language-aware CSS classes
 */
export function getLanguageClasses(baseClasses: string, language?: Language) {
  const languageClass = language === 'mm' ? 'font-myanmar' : ''
  return `${baseClasses} ${languageClass}`.trim()
}

/**
 * Get combined theme and language classes
 */
export function getThemeLanguageClasses(
  baseClasses: string, 
  theme?: 'light' | 'dark', 
  language?: Language
) {
  const themeClasses = getThemeClasses(baseClasses, theme)
  return getLanguageClasses(themeClasses, language)
}

/**
 * Force re-render of components by dispatching events
 */
export function forceThemeUpdate(theme: 'light' | 'dark') {
  if (typeof window === 'undefined') return

  window.dispatchEvent(new CustomEvent('theme-changed', {
    detail: { theme }
  }))
}

/**
 * Force re-render of components by dispatching events
 */
export function forceLanguageUpdate(language: Language) {
  if (typeof window === 'undefined') return

  window.dispatchEvent(new CustomEvent('language-changed', {
    detail: { language }
  }))
}

/**
 * Initialize theme and language on page load
 */
export function initializeThemeAndLanguage() {
  if (typeof window === 'undefined') return

  try {
    const savedTheme = localStorage.getItem('bitstech-theme') as Theme || 'light'
    const savedLanguage = localStorage.getItem('bitstech-language') as Language || 'mm'

    const actualTheme = applyThemeToDOM(savedTheme)
    applyLanguageToDOM(savedLanguage)

    return { theme: actualTheme, language: savedLanguage }
  } catch (error) {
    console.error('Error initializing theme and language:', error)
    return { theme: 'light' as const, language: 'mm' as const }
  }
}

/**
 * Sync theme and language across tabs
 */
export function syncThemeAndLanguage(theme: Theme, language: Language) {
  if (typeof window === 'undefined') return

  try {
    // Save to localStorage
    localStorage.setItem('bitstech-theme', theme)
    localStorage.setItem('bitstech-language', language)

    // Broadcast to other tabs
    localStorage.setItem('bitstech_theme_sync', JSON.stringify({
      theme,
      timestamp: Date.now()
    }))

    localStorage.setItem('bitstech_language_sync', JSON.stringify({
      language,
      timestamp: Date.now()
    }))
  } catch (error) {
    console.error('Error syncing theme and language:', error)
  }
}

/**
 * Listen for theme and language changes from other tabs
 */
export function setupCrossTabSync(
  onThemeChange: (theme: Theme) => void,
  onLanguageChange: (language: Language) => void
) {
  if (typeof window === 'undefined') return

  const handleStorageChange = (event: StorageEvent) => {
    try {
      if (event.key === 'bitstech_theme_sync' && event.newValue) {
        const data = JSON.parse(event.newValue)
        if (data.theme) {
          onThemeChange(data.theme)
        }
      }

      if (event.key === 'bitstech_language_sync' && event.newValue) {
        const data = JSON.parse(event.newValue)
        if (data.language) {
          onLanguageChange(data.language)
        }
      }
    } catch (error) {
      console.error('Error parsing sync data:', error)
    }
  }

  window.addEventListener('storage', handleStorageChange)

  return () => {
    window.removeEventListener('storage', handleStorageChange)
  }
}

/**
 * Get current theme from DOM
 */
export function getCurrentThemeFromDOM(): 'light' | 'dark' {
  if (typeof window === 'undefined') return 'light'
  
  return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
}

/**
 * Get current language from DOM
 */
export function getCurrentLanguageFromDOM(): Language {
  if (typeof window === 'undefined') return 'mm'
  
  return document.documentElement.classList.contains('lang-mm') ? 'mm' : 'en'
}
