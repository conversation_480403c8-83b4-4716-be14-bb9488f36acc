'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { setCurrentCurrency as setCentralCurrency, getCurrentCurrency } from '@/lib/currency'
import {
  applyThemeToDOM,
  applyLanguageToDOM,
  forceThemeUpdate,
  forceLanguageUpdate,
  syncThemeAndLanguage,
  setupCrossTabSync,
  type Theme,
  type Language
} from '@/lib/theme-utils'

type ColorScheme = 'blue' | 'green' | 'purple' | 'orange' | 'pink' | 'indigo'
type Currency = 'MMK' | 'USD' | 'THB'

interface ExchangeRate {
  rate: number
  symbol: string
  name: string
  flag: string
}

interface ThemeContextType {
  theme: Theme
  colorScheme: ColorScheme
  setTheme: (theme: Theme) => void
  setColorScheme: (scheme: ColorScheme) => void
  actualTheme: 'light' | 'dark'
  toggleTheme: () => void

  // Language
  language: Language
  setLanguage: (language: Language) => void
  toggleLanguage: () => void

  // Currency
  currency: Currency
  setCurrency: (currency: Currency) => void
  toggleCurrency: () => void
  exchangeRates: Record<Currency, ExchangeRate>
  setExchangeRates: (rates: Record<Currency, ExchangeRate>) => void
  formatCurrency: (amount: number) => string
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  // Get initial values from the no-flash script
  const getInitialTheme = (): Theme => {
    if (typeof window !== 'undefined') {
      // First check global variable from script
      if ((window as any).__BITSTECH_THEME__) {
        const scriptTheme = (window as any).__BITSTECH_THEME__
        console.log('🎨 Using theme from script:', scriptTheme)
        return scriptTheme === 'dark' ? 'dark' : 'light'
      }
      // Fallback to localStorage
      const savedTheme = localStorage.getItem('bitstech-theme') as Theme
      if (savedTheme) {
        console.log('🎨 Using theme from localStorage:', savedTheme)
        return savedTheme
      }
    }
    console.log('🎨 Using default theme: light')
    return 'light'
  }

  const getInitialLanguage = (): Language => {
    if (typeof window !== 'undefined') {
      // First check global variable from script
      if ((window as any).__BITSTECH_LANGUAGE__) {
        const scriptLanguage = (window as any).__BITSTECH_LANGUAGE__
        console.log('🌐 Using language from script:', scriptLanguage)
        return scriptLanguage
      }
      // Fallback to localStorage
      const savedLanguage = localStorage.getItem('bitstech-language') as Language
      if (savedLanguage) {
        console.log('🌐 Using language from localStorage:', savedLanguage)
        return savedLanguage
      }
    }
    console.log('🌐 Using default language: mm')
    return 'mm'
  }

  // Initialize with values from the script to prevent hydration mismatch
  const [mounted, setMounted] = useState(false)
  const [theme, setTheme] = useState<Theme>('light') // Start with safe default
  const [colorScheme, setColorScheme] = useState<ColorScheme>('blue')
  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light') // Start with safe default
  const [language, setLanguage] = useState<Language>('mm') // Start with safe default
  const [currency, setCurrency] = useState<Currency>('MMK')

  // Set mounted to true and initialize from script/localStorage
  useEffect(() => {
    setMounted(true)

    if (typeof window !== 'undefined') {
      // Get initial values
      const initialTheme = getInitialTheme()
      const initialLanguage = getInitialLanguage()
      const savedColorScheme = localStorage.getItem('bitstech-colorScheme') as ColorScheme || 'blue'

      console.log('🎨 ThemeProvider initializing with:', {
        initialTheme,
        initialLanguage,
        savedColorScheme,
        scriptTheme: (window as any).__BITSTECH_THEME__,
        scriptLanguage: (window as any).__BITSTECH_LANGUAGE__
      })

      // Set all values at once to prevent multiple re-renders
      setTheme(initialTheme)
      setLanguage(initialLanguage)
      setColorScheme(savedColorScheme)

      // Calculate actual theme
      const newActualTheme = initialTheme === 'system'
        ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
        : initialTheme as 'light' | 'dark'

      setActualTheme(newActualTheme)

      // Clear global variables after reading them
      setTimeout(() => {
        delete (window as any).__BITSTECH_THEME__
        delete (window as any).__BITSTECH_LANGUAGE__
      }, 100)
    }
  }, [])

  // Default exchange rates
  const [exchangeRates, setExchangeRates] = useState<Record<Currency, ExchangeRate>>({
    MMK: { rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
    USD: { rate: 0.00048, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
    THB: { rate: 0.017, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
  })

  useEffect(() => {
    // Load currency and setup event listeners only on client side
    if (typeof window !== 'undefined' && mounted) {
      // Get currency from centralized system
      const currentCurrency = getCurrentCurrency()
      const savedCurrency = currentCurrency.code as Currency

      if (savedCurrency && ['MMK', 'USD', 'THB'].includes(savedCurrency)) {
        setCurrency(savedCurrency)
      }

      // Listen for currency changes from centralized system
      const handleCurrencyChange = (event: any) => {
        const newCurrency = event.detail.currency?.code || event.detail.currency
        if (newCurrency && newCurrency !== currency && ['MMK', 'USD', 'THB'].includes(newCurrency)) {
          console.log('🔄 Theme context received currency change:', newCurrency)
          // Don't update currency here to prevent loops
          // Let CurrencyProvider handle currency changes
        }
      }

      window.addEventListener('currencyChanged', handleCurrencyChange)

      // Load exchange rates
      const savedRates = localStorage.getItem('bitstech_exchange_rates')
      if (savedRates) {
        try {
          const parsedRates = JSON.parse(savedRates)
          setExchangeRates(parsedRates)
        } catch (error) {
          console.error('Error loading exchange rates:', error)
        }
      }

      // Cleanup listener on unmount
      return () => {
        window.removeEventListener('currencyChanged', handleCurrencyChange)
      }
    }
  }, [currency])

  // Save currency preference - NO SYNC TO PREVENT LOOPS
  useEffect(() => {
    if (typeof window !== 'undefined' && mounted) {
      localStorage.setItem('bitstech-currency', currency)
      console.log('🔄 Theme currency saved to localStorage:', currency)
    }
  }, [currency, mounted])

  // Real-time cross-tab synchronization
  useEffect(() => {
    if (typeof window === 'undefined') return

    const cleanup = setupCrossTabSync(
      (newTheme) => {
        if (newTheme !== theme) {
          setTheme(newTheme)
          console.log('🔄 Theme synced from another tab:', newTheme)
        }
      },
      (newLanguage) => {
        if (newLanguage !== language) {
          setLanguage(newLanguage)
          console.log('🔄 Language synced from another tab:', newLanguage)
        }
      }
    )

    const handleStorageChange = (event: StorageEvent) => {
      try {
        if (event.key === 'bitstech_currency_sync' && event.newValue) {
          const data = JSON.parse(event.newValue)
          if (data.currency && data.currency !== currency && ['MMK', 'USD', 'THB'].includes(data.currency)) {
            setCurrency(data.currency)
            console.log('🔄 Currency synced from another tab:', data.currency)
          }
        }
      } catch (error) {
        console.error('Error parsing sync data:', error)
      }
    }

    // Listen for storage changes from other tabs
    window.addEventListener('storage', handleStorageChange)

    // Listen for custom events within the same tab
    const handleCurrencyChange = (event: CustomEvent) => {
      const newCurrency = event.detail.currency
      if (newCurrency && newCurrency !== currency && ['MMK', 'USD', 'THB'].includes(newCurrency)) {
        setCurrency(newCurrency)
      }
    }

    window.addEventListener('currency-changed', handleCurrencyChange as EventListener)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('currency-changed', handleCurrencyChange as EventListener)
      if (cleanup) cleanup()
    }
  }, []) // Remove dependencies to prevent infinite loop

  // Listen for currency and exchange rate updates from settings
  useEffect(() => {
    const handleThemeCurrencyUpdate = (event: CustomEvent) => {
      const newCurrency = event.detail.currency
      if (newCurrency && ['MMK', 'USD', 'THB'].includes(newCurrency)) {
        setCurrency(newCurrency)
      }
    }

    const handleExchangeRatesUpdate = (event: CustomEvent) => {
      const newRates = event.detail.rates
      if (newRates) {
        setExchangeRates(newRates)
      }
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('theme-currency-update', handleThemeCurrencyUpdate as EventListener)
      window.addEventListener('exchange-rates-updated', handleExchangeRatesUpdate as EventListener)

      return () => {
        window.removeEventListener('theme-currency-update', handleThemeCurrencyUpdate as EventListener)
        window.removeEventListener('exchange-rates-updated', handleExchangeRatesUpdate as EventListener)
      }
    }
  }, [])

  // Apply theme changes
  useEffect(() => {
    if (typeof window !== 'undefined' && mounted) {
      console.log('🎨 Applying theme change:', theme)
      const actualTheme = applyThemeToDOM(theme)
      setActualTheme(actualTheme)
      forceThemeUpdate(actualTheme)

      // Save to localStorage
      localStorage.setItem('bitstech-theme', theme)

      // Sync across tabs
      syncThemeAndLanguage(theme, language)
    }
  }, [theme, mounted])

  // Apply language changes
  useEffect(() => {
    if (typeof window !== 'undefined' && mounted) {
      console.log('🌐 Applying language change:', language)
      applyLanguageToDOM(language)
      forceLanguageUpdate(language)

      // Save to localStorage
      localStorage.setItem('bitstech-language', language)

      // Sync across tabs
      syncThemeAndLanguage(theme, language)
    }
  }, [language, mounted])

  useEffect(() => {
    if (typeof window !== 'undefined' && mounted) {
      const root = window.document.documentElement

      // Remove previous color scheme classes
      root.classList.remove('scheme-blue', 'scheme-green', 'scheme-purple', 'scheme-orange', 'scheme-pink', 'scheme-indigo')

      // Add new color scheme class
      root.classList.add(`scheme-${colorScheme}`)

      // Update CSS custom properties for the color scheme
      const colorSchemes = {
        blue: {
          primary: '59 130 246', // blue-500
          primaryForeground: '255 255 255',
          secondary: '239 246 255', // blue-50
          accent: '147 197 253', // blue-300
        },
        green: {
          primary: '34 197 94', // green-500
          primaryForeground: '255 255 255',
          secondary: '240 253 244', // green-50
          accent: '134 239 172', // green-300
        },
        purple: {
          primary: '147 51 234', // purple-500
          primaryForeground: '255 255 255',
          secondary: '250 245 255', // purple-50
          accent: '196 181 253', // purple-300
        },
        orange: {
          primary: '249 115 22', // orange-500
          primaryForeground: '255 255 255',
          secondary: '255 247 237', // orange-50
          accent: '253 186 116', // orange-300
        },
        pink: {
          primary: '236 72 153', // pink-500
          primaryForeground: '255 255 255',
          secondary: '253 242 248', // pink-50
          accent: '249 168 212', // pink-300
        },
        indigo: {
          primary: '99 102 241', // indigo-500
          primaryForeground: '255 255 255',
          secondary: '238 242 255', // indigo-50
          accent: '165 180 252', // indigo-300
        }
      }

      const colors = colorSchemes[colorScheme]

      root.style.setProperty('--primary', colors.primary)
      root.style.setProperty('--primary-foreground', colors.primaryForeground)
      root.style.setProperty('--secondary', colors.secondary)
      root.style.setProperty('--accent', colors.accent)

      // Save color scheme preference
      localStorage.setItem('bitstech-colorScheme', colorScheme)
    }
  }, [colorScheme, mounted])

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light'
    console.log('🎨 Toggling theme from', theme, 'to', newTheme)
    setTheme(newTheme)
  }

  const toggleLanguage = () => {
    const newLanguage = language === 'en' ? 'mm' : 'en'
    console.log('🌐 Toggling language from', language, 'to', newLanguage)
    setLanguage(newLanguage)
  }

  const toggleCurrency = () => {
    setCurrency(prev => {
      const newCurrency = prev === 'MMK' ? 'USD' : prev === 'USD' ? 'THB' : 'MMK'

      // Broadcast currency change to CurrencyProvider
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('currency-toggle', {
          detail: { newCurrency }
        }))
      }

      return newCurrency
    })
  }

  const formatCurrency = (amount: number): string => {
    const rate = exchangeRates[currency]
    if (!rate) return `${amount} K`

    // Convert from MMK to target currency
    const convertedAmount = currency === 'MMK' ? amount : amount * rate.rate

    let formattedAmount: string
    if (currency === 'MMK') {
      // Format MMK without decimals
      formattedAmount = Math.round(convertedAmount).toLocaleString()
    } else {
      // Format other currencies with 2 decimals
      formattedAmount = convertedAmount.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }

    // Return formatted currency with symbol
    if (currency === 'MMK') {
      return `${formattedAmount} ${rate.symbol}`
    } else {
      return `${rate.symbol}${formattedAmount}`
    }
  }

  const value = {
    theme,
    colorScheme,
    setTheme,
    setColorScheme,
    actualTheme,
    toggleTheme,
    language,
    setLanguage,
    toggleLanguage,
    currency,
    setCurrency,
    toggleCurrency,
    exchangeRates,
    setExchangeRates,
    formatCurrency
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    // Return default values instead of throwing error during SSR
    if (typeof window === 'undefined') {
      return {
        theme: 'light' as Theme,
        colorScheme: 'blue' as ColorScheme,
        setTheme: () => {},
        setColorScheme: () => {},
        actualTheme: 'light' as 'light' | 'dark',
        toggleTheme: () => {},
        language: 'mm' as Language,
        setLanguage: () => {},
        toggleLanguage: () => {},
        currency: 'MMK' as Currency,
        setCurrency: () => {},
        toggleCurrency: () => {},
        exchangeRates: {
          MMK: { rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
          USD: { rate: 0.00048, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
          THB: { rate: 0.017, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
        },
        setExchangeRates: () => {},
        formatCurrency: (amount: number) => `${amount} K`
      }
    }
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
