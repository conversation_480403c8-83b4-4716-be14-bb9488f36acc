'use client'

import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'
import { useTheme } from '@/contexts/theme-context'
import { useThemeLanguage } from '@/hooks/use-theme-language'
import { ThemeAwareContent, ThemeAwareButton } from '@/components/theme-aware-content'

export default function HomePage() {
  const router = useRouter()
  const { theme, language, toggleTheme, toggleLanguage } = useTheme()
  const { getClasses, isMyanmarLanguage } = useThemeLanguage()

  const handleLogin = () => {
    router.push('/auth/login')
  }

  const handleRegister = () => {
    router.push('/auth/register')
  }

  const handleDemo = () => {
    router.push('/pos')
  }

  // Log theme and language for debugging
  useEffect(() => {
    console.log('🏠 Home page theme and language:', { theme, language })
    console.log('🏠 DOM classes:', {
      html: document.documentElement.className,
      body: document.body.className,
      htmlDataTheme: document.documentElement.getAttribute('data-theme'),
      htmlDataLanguage: document.documentElement.getAttribute('data-language')
    })
  }, [theme, language])

  // Load content from settings
  useEffect(() => {
    const savedEnglish = localStorage.getItem('homepage-content-en')
    const savedMyanmar = localStorage.getItem('homepage-content-mm')

    if (savedEnglish) {
      const englishData = JSON.parse(savedEnglish)
      setContent(prev => ({ ...prev, en: englishData }))
    }
    if (savedMyanmar) {
      const myanmarData = JSON.parse(savedMyanmar)
      setContent(prev => ({ ...prev, mm: myanmarData }))
    }
  }, [])

  // Language content
  const [content, setContent] = useState({
    en: {
      title: "BitsTech POS",
      subtitle: "Complete Point of Sale System for Myanmar Businesses",
      description: "Revolutionary POS solution with real-time inventory, multi-payment support (KBZ Pay, Wave Money, AYA Pay), and powerful analytics. Built specifically for Myanmar entrepreneurs.",
      getStarted: "Get Started",
      tryDemo: "Try Demo",
      login: "Login",
      features: "Complete POS Features for Your Business",

      // Core POS Features
      feature1: "🏪 Advanced POS Terminal",
      feature1Desc: "Lightning-fast checkout with barcode scanning, receipt printing, and support for cash, KBZ Pay, Wave Money, AYA Pay payments",

      feature2: "📦 Smart Inventory Management",
      feature2Desc: "Real-time stock tracking, low stock alerts, automated reordering, multi-location support, and detailed inventory reports",

      feature3: "📊 Powerful Analytics & Reports",
      feature3Desc: "Comprehensive sales analytics, profit analysis, customer insights, trending products, and customizable business dashboards",

      feature4: "👥 Customer Management",
      feature4Desc: "Customer profiles, purchase history, loyalty points, targeted promotions, and customer behavior analytics",

      feature5: "🌐 Multi-Language & Currency",
      feature5Desc: "Complete Myanmar and English interface with MMK, USD, THB currency support and localized number formats",

      feature6: "📱 Mobile & Cross-Platform",
      feature6Desc: "Works seamlessly on smartphones, tablets, and computers with offline capabilities and cloud synchronization",

      stats: {
        businesses: "500+ Businesses",
        transactions: "1M+ Transactions",
        uptime: "99.9% Uptime",
        support: "24/7 Support"
      },

      footer: "Built with ❤️ for Myanmar businesses"
    },
    mm: {
      title: "BitsTech POS",
      subtitle: "မြန်မာနိုင်ငံရှိ လုပ်ငန်းများအတွက် ပြီးပြည့်စုံသော ရောင်းချရာနေရာ စနစ်",
      description: "အချိန်နှင့်တပြေးညီ ကုန်စာရင်း၊ ငွေပေးချေမှု နည်းလမ်းများစွာ (KBZ Pay, Wave Money, AYA Pay) နှင့် အစွမ်းထက် ခွဲခြမ်းစိတ်ဖြာမှုများပါရှိသော တော်လှန်ရေးဆန်သော POS ဖြေရှင်းချက်။ မြန်မာ လုပ်ငန်းရှင်များအတွက် အထူးတလည် တည်ဆောက်ထားပါသည်။",
      getStarted: "စတင်ရန်",
      tryDemo: "စမ်းကြည့်ရန်",
      login: "ဝင်ရောက်ရန်",
      features: "သင့်လုပ်ငန်းအတွက် ပြီးပြည့်စုံသော POS လုပ်ဆောင်ချက်များ",

      // Core POS Features
      feature1: "🏪 အဆင့်မြင့် POS ဆိုင်ကောင်တာ",
      feature1Desc: "လျှပ်စီးမြန်နှုန်းရှိ ငွေတောင်းခံမှု၊ ဘားကုဒ် စကင်နာ၊ ဘောင်ချာ ပရင့်ထုတ်မှု နှင့် ငွေသား၊ KBZ Pay၊ Wave Money၊ AYA Pay ငွေပေးချေမှု ပံ့ပိုးမှု",

      feature2: "📦 စမတ် ကုန်စာရင်း စီမံခန့်ခွဲမှု",
      feature2Desc: "အချိန်နှင့်တပြေးညီ ကုန်စတော့ ခြေရာခံမှု၊ ကုန်နည်းသတိပေးချက်များ၊ အလိုအလျောက် ပြန်လည်မှာယူမှု၊ နေရာများစွာ ပံ့ပိုးမှု နှင့် အသေးစိတ် ကုန်စာရင်း အစီရင်ခံစာများ",

      feature3: "📊 အစွမ်းထက် ခွဲခြမ်းစိတ်ဖြာမှု နှင့် အစီရင်ခံစာများ",
      feature3Desc: "ပြီးပြည့်စုံသော ရောင်းအား ခွဲခြမ်းစိတ်ဖြာမှု၊ အမြတ်ခွဲခြမ်းစိတ်ဖြာမှု၊ ဖောက်သည် အချက်အလက်များ၊ ရေပန်းစားသော ကုန်ပစ္စည်းများ နှင့် စိတ်ကြိုက်ပြင်ဆင်နိုင်သော လုပ်ငန်း ဒက်ရှ်ဘုတ်များ",

      feature4: "👥 ဖောက်သည် စီမံခန့်ခွဲမှု",
      feature4Desc: "ဖောက်သည် ပရိုဖိုင်များ၊ ဝယ်ယူမှု သမိုင်း၊ သစ္စာရှိမှု အမှတ်များ၊ ပစ်မှတ်ထားသော ပရိုမိုးရှင်းများ နှင့် ဖောက်သည် အပြုအမူ ခွဲခြမ်းစိတ်ဖြာမှု",

      feature5: "🌐 ဘာသာစကားများစွာ နှင့် ငွေကြေး",
      feature5Desc: "မြန်မာနှင့် အင်္ဂလိပ်ဘာသာ အပြည့်အဝ အင်တာဖေ့စ်နှင့် MMK၊ USD၊ THB ငွေကြေး ပံ့ပိုးမှု နှင့် ဒေသဆိုင်ရာ နံပါတ်ပုံစံများ",

      feature6: "📱 မိုဘိုင်း နှင့် ပလပ်ဖောင်းများစွာ",
      feature6Desc: "စမတ်ဖုန်း၊ တက်ဘလက် နှင့် ကွန်ပျူတာများတွင် ပြေပြစ်စွာ အလုပ်လုပ်ပြီး အင်တာနက်မရှိချိန် အသုံးပြုနိုင်မှု နှင့် cloud ထပ်တူပြုမှု",

      stats: {
        businesses: "၅၀၀+ လုပ်ငန်းများ",
        transactions: "၁သန်း+ ငွေပေးငွေယူများ",
        uptime: "၉၉.၉% အလုပ်လုပ်ချိန်",
        support: "၂၄/၇ ပံ့ပိုးမှု"
      },

      footer: "မြန်မာနိုင်ငံရှိ လုပ်ငန်းများအတွက် ချစ်ခြင်းမေတ္တာဖြင့် ❤️ တည်ဆောက်ထားပါသည်"
    }
  })

  return (
    <div
      className={getClasses("min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300")}
      suppressHydrationWarning
    >
      {/* Header */}
      <header className={getClasses("border-b border-gray-200 dark:border-gray-800 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm sticky top-0 z-50 transition-colors duration-300")} suppressHydrationWarning>
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div>
                <h1 className="text-xl font-bold text-blue-600">
                  {content[language].title}
                </h1>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex items-center space-x-4">
              <ThemeAwareContent fallback={
                <div className="flex items-center space-x-4">
                  <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-300">
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                    </svg>
                  </button>
                  <button className="px-3 py-1 rounded-lg text-sm hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-300 font-myanmar">
                    မြန်မာ
                  </button>
                  <button className="px-4 py-2 rounded-lg text-sm hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-300 font-myanmar">
                    ဝင်ရောက်ရန်
                  </button>
                </div>
              }>
                {({ theme, language, getClasses, isDarkTheme }) => (
                  <>
                    <ThemeAwareButton
                      onClick={toggleTheme}
                      className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-300"
                      suppressHydrationWarning
                    >
                      {isDarkTheme ? (
                        <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                      ) : (
                        <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                        </svg>
                      )}
                    </ThemeAwareButton>

                    <ThemeAwareButton
                      onClick={toggleLanguage}
                      className="px-3 py-1 rounded-lg text-sm hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-300"
                      suppressHydrationWarning
                    >
                      {language === 'en' ? 'မြန်မာ' : 'English'}
                    </ThemeAwareButton>

                    <ThemeAwareButton
                      onClick={handleLogin}
                      className="px-4 py-2 rounded-lg text-sm hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-300"
                      suppressHydrationWarning
                    >
                      {content[language].login}
                    </ThemeAwareButton>
                  </>
                )}
              </ThemeAwareContent>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-5xl mx-auto text-center">
          <h1 className={getClasses('text-3xl sm:text-4xl lg:text-5xl font-bold mb-3')}>
            {content[language].title}
          </h1>
          <p className={getClasses('text-lg sm:text-xl mb-3 text-blue-600 dark:text-blue-400 font-semibold')}>
            {content[language].subtitle}
          </p>
          <p className={getClasses('text-base mb-8 max-w-4xl mx-auto text-gray-700 dark:text-gray-300')}>
            {content[language].description}
          </p>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div className={getClasses('p-4 rounded-lg bg-blue-50 dark:bg-gray-800')}>
              <div className={getClasses('text-2xl font-bold text-blue-600 dark:text-blue-400')}>
                500+
              </div>
              <div className={getClasses('text-sm text-gray-600 dark:text-gray-400')}>
                {language === 'en' ? 'Businesses' : 'လုပ်ငန်းများ'}
              </div>
            </div>
            <div className={getClasses('p-4 rounded-lg bg-green-50 dark:bg-gray-800')}>
              <div className={getClasses('text-2xl font-bold text-green-600 dark:text-green-400')}>
                1M+
              </div>
              <div className={getClasses('text-sm text-gray-600 dark:text-gray-400')}>
                {language === 'en' ? 'Transactions' : 'ငွေပေးငွေယူများ'}
              </div>
            </div>
            <div className={getClasses('p-4 rounded-lg bg-purple-50 dark:bg-gray-800')}>
              <div className={getClasses('text-2xl font-bold text-purple-600 dark:text-purple-400')}>
                99.9%
              </div>
              <div className={getClasses('text-sm text-gray-600 dark:text-gray-400')}>
                {language === 'en' ? 'Uptime' : 'အလုပ်လုပ်ချိန်'}
              </div>
            </div>
            <div className={getClasses('p-4 rounded-lg bg-orange-50 dark:bg-gray-800')}>
              <div className={getClasses('text-2xl font-bold text-orange-600 dark:text-orange-400')}>
                24/7
              </div>
              <div className={getClasses('text-sm text-gray-600 dark:text-gray-400')}>
                {language === 'en' ? 'Support' : 'ပံ့ပိုးမှု'}
              </div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={handleRegister}
              className={getClasses('bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg')}
            >
              {content[language].getStarted}
            </button>
            <button
              onClick={handleDemo}
              className={getClasses('border-2 border-blue-600 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 px-8 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105')}
            >
              {content[language].tryDemo}
            </button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className={getClasses('py-12 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-800/30')}>
        <div className="max-w-7xl mx-auto">
          <h2 className={getClasses('text-2xl sm:text-3xl font-bold text-center mb-8')}>
            {content[language].features}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Feature 1 - POS Terminal */}
            <div className={getClasses('p-5 rounded-xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 transform hover:scale-105')}>
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mb-3">
                <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className={getClasses('text-base font-bold mb-2')}>
                {content[language].feature1}
              </h3>
              <p className={getClasses('text-sm text-gray-600 dark:text-gray-300')}>
                {content[language].feature1Desc}
              </p>
            </div>

            {/* Feature 2 - Inventory */}
            <div className={getClasses('p-5 rounded-xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 transform hover:scale-105')}>
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mb-3">
                <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
              <h3 className={getClasses('text-base font-bold mb-2')}>
                {content[language].feature2}
              </h3>
              <p className={getClasses('text-sm text-gray-600 dark:text-gray-300')}>
                {content[language].feature2Desc}
              </p>
            </div>

            {/* Feature 3 - Analytics */}
            <div className={getClasses('p-5 rounded-xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 transform hover:scale-105')}>
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mb-3">
                <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className={getClasses('text-base font-bold mb-2')}>
                {content[language].feature3}
              </h3>
              <p className={getClasses('text-sm text-gray-600 dark:text-gray-300')}>
                {content[language].feature3Desc}
              </p>
            </div>

            {/* Feature 4 - Customer Management */}
            <div className={getClasses('p-5 rounded-xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 transform hover:scale-105')}>
              <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mb-3">
                <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className={getClasses('text-base font-bold mb-2')}>
                {content[language].feature4}
              </h3>
              <p className={getClasses('text-sm text-gray-600 dark:text-gray-300')}>
                {content[language].feature4Desc}
              </p>
            </div>

            {/* Feature 5 - Multi-Language */}
            <div className={getClasses('p-5 rounded-xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 transform hover:scale-105')}>
              <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mb-3">
                <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                </svg>
              </div>
              <h3 className={getClasses('text-base font-bold mb-2')}>
                {content[language].feature5}
              </h3>
              <p className={getClasses('text-sm text-gray-600 dark:text-gray-300')}>
                {content[language].feature5Desc}
              </p>
            </div>

            {/* Feature 6 - Mobile & Cross-Platform */}
            <div className={getClasses('p-5 rounded-xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 transform hover:scale-105')}>
              <div className="w-10 h-10 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg flex items-center justify-center mb-3">
                <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className={getClasses('text-base font-bold mb-2')}>
                {content[language].feature6}
              </h3>
              <p className={getClasses('text-sm text-gray-600 dark:text-gray-300')}>
                {content[language].feature6Desc}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className={getClasses('py-8 px-4 sm:px-6 lg:px-8 border-t border-gray-200 dark:border-gray-800')}>
        <div className="max-w-6xl mx-auto text-center">
          <p className={getClasses('text-sm text-gray-600 dark:text-gray-400')}>
            © 2024 BitsTech POS. {content[language].footer}
          </p>
        </div>
      </footer>

      {/* Debug Info - Only in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 bg-black/80 text-white p-3 rounded-lg text-xs font-mono z-50">
          <div>Theme: {theme}</div>
          <div>Language: {language}</div>
          <div>Mounted: {String(getClasses !== undefined)}</div>
          <div>HTML Classes: {typeof window !== 'undefined' ? document.documentElement.className : 'SSR'}</div>
        </div>
      )}
    </div>
  )
}
