import type { Metada<PERSON> } from "next";
import "./globals.css";
import { AuthProvider } from "@/contexts/auth-context";
import { SettingsProvider } from "@/contexts/settings-context";
import { ThemeProvider } from "@/contexts/theme-context";
import { ThemeCustomizationProvider } from "@/contexts/theme-customization-context";
import { CurrencyProvider } from "@/contexts/currency-context";
import { NotificationProvider } from "@/contexts/notification-context";
import { ThemeEnforcer } from "@/components/theme-enforcer";

export const metadata: Metadata = {
  title: "BitsTech POS System",
  description: "Modern Point-of-Sale Solution for Myanmar Businesses",
  icons: {
    icon: '/favicon.ico',
    apple: '/icons/apple-touch-icon.png',
  },
  manifest: '/manifest.json',
};

export const viewport = {
  width: 'device-width',
  initialScale: 1.0,
  maximumScale: 1.0,
  userScalable: false,
  shrinkToFit: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Prevent theme and language flashing
              (function() {
                try {
                  // Get saved preferences
                  const theme = localStorage.getItem('bitstech-theme') || 'light';
                  const language = localStorage.getItem('bitstech-language') || 'mm';

                  // Determine actual theme
                  let actualTheme = theme;
                  if (theme === 'system') {
                    actualTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                  }

                  // Apply classes immediately
                  const html = document.documentElement;
                  const body = document.body;

                  // Disable transitions temporarily
                  html.classList.add('theme-loading');

                  // Remove existing classes
                  html.classList.remove('light', 'dark', 'lang-en', 'lang-mm', 'font-myanmar');
                  body.classList.remove('light', 'dark', 'lang-en', 'lang-mm', 'font-myanmar');

                  // Add correct classes
                  html.classList.add(actualTheme, 'lang-' + language);
                  body.classList.add(actualTheme, 'lang-' + language);

                  // Add Myanmar font if needed
                  if (language === 'mm') {
                    html.classList.add('font-myanmar');
                    body.classList.add('font-myanmar');
                  }

                  // Set attributes
                  html.setAttribute('data-theme', actualTheme);
                  html.setAttribute('data-language', language);
                  html.setAttribute('lang', language === 'mm' ? 'my' : 'en');
                  html.style.colorScheme = actualTheme;

                  // Re-enable transitions after a brief delay
                  setTimeout(() => {
                    html.classList.remove('theme-loading');
                  }, 50);

                } catch (e) {
                  console.error('Theme initialization error:', e);
                }
              })();
            `,
          }}
        />
      </head>
      <body
        className="font-sans antialiased transition-colors duration-300"
        suppressHydrationWarning
      >
        <CurrencyProvider>
          <ThemeProvider>
            <ThemeCustomizationProvider>
              <SettingsProvider>
                <AuthProvider>
                  <NotificationProvider>
                    <ThemeEnforcer />
                    {children}
                  </NotificationProvider>
                </AuthProvider>
              </SettingsProvider>
            </ThemeCustomizationProvider>
          </ThemeProvider>
        </CurrencyProvider>
      </body>
    </html>
  );
}
